import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and, count } from 'drizzle-orm';
import { deductCreditForSession, refundCreditForSession } from './credit-service';
import { validateWorkoutConfirmation } from './business-rules-service';

export type WorkoutStatus = 'scheduled' | 'confirmed' | 'completed' | 'cancelled';
export type ParticipantStatus = 'enrolled' | 'confirmed' | 'cancelled';

export interface StatusTransitionResult {
  success: boolean;
  message: string;
  newStatus?: WorkoutStatus;
}

export interface ParticipantStatusTransitionResult {
  success: boolean;
  message: string;
  newStatus?: ParticipantStatus;
  creditDeducted?: boolean;
}

/**
 * Validates if a workout status transition is allowed
 */
export function validateWorkoutStatusTransition(
  currentStatus: WorkoutStatus,
  newStatus: WorkoutStatus
): { valid: boolean; message: string } {
  const transitions: Record<WorkoutStatus, WorkoutStatus[]> = {
    scheduled: ['confirmed', 'cancelled'],
    confirmed: ['completed', 'cancelled'],
    completed: [], // No transitions from completed
    cancelled: [], // No transitions from cancelled
  };

  const allowedTransitions = transitions[currentStatus] || [];
  
  if (!allowedTransitions.includes(newStatus)) {
    return {
      valid: false,
      message: `Cannot transition from ${currentStatus} to ${newStatus}`,
    };
  }

  return { valid: true, message: 'Transition allowed' };
}

/**
 * Validates if a participant status transition is allowed
 */
export function validateParticipantStatusTransition(
  currentStatus: ParticipantStatus,
  newStatus: ParticipantStatus
): { valid: boolean; message: string } {
  const transitions: Record<ParticipantStatus, ParticipantStatus[]> = {
    enrolled: ['confirmed', 'cancelled'],
    confirmed: ['cancelled'], // Can cancel confirmed participants
    cancelled: [], // No transitions from cancelled
  };

  const allowedTransitions = transitions[currentStatus] || [];
  
  if (!allowedTransitions.includes(newStatus)) {
    return {
      valid: false,
      message: `Cannot transition participant from ${currentStatus} to ${newStatus}`,
    };
  }

  return { valid: true, message: 'Transition allowed' };
}

/**
 * Transitions a workout to a new status with business rule validation
 */
export async function transitionWorkoutStatus(
  workoutId: string,
  newStatus: WorkoutStatus,
  trainerId: string
): Promise<StatusTransitionResult> {
  try {
    // Get workout with current status and participant count
    const [workout] = await db
      .select({
        id: workouts.id,
        title: workouts.title,
        status: workouts.status,
        minParticipants: workouts.minParticipants,
        maxParticipants: workouts.maxParticipants,
        trainerId: workouts.trainerId,
      })
      .from(workouts)
      .where(and(eq(workouts.id, workoutId), eq(workouts.trainerId, trainerId)));

    if (!workout) {
      return { success: false, message: 'Workout not found' };
    }

    // Validate status transition
    const transitionValidation = validateWorkoutStatusTransition(
      workout.status as WorkoutStatus,
      newStatus
    );

    if (!transitionValidation.valid) {
      return { success: false, message: transitionValidation.message };
    }

    // Get participant count
    const participantCountResult = await db
      .select({ participantCount: count() })
      .from(workoutParticipants)
      .where(
        and(
          eq(workoutParticipants.workoutId, workoutId),
          eq(workoutParticipants.status, 'enrolled')
        )
      );

    const participantCount = participantCountResult[0]?.participantCount || 0;

    // Business rule validations
    if (newStatus === 'confirmed') {
      const confirmationValidation = await validateWorkoutConfirmation(workoutId, trainerId);
      if (!confirmationValidation.valid) {
        return {
          success: false,
          message: confirmationValidation.message,
        };
      }

      // When confirming, automatically confirm all enrolled participants and deduct credits
      await confirmAllParticipants(workoutId, workout.title);
    }

    if (newStatus === 'completed') {
      if (workout.status !== 'confirmed') {
        return {
          success: false,
          message: 'Cannot complete workout: workout must be confirmed first',
        };
      }
    }

    if (newStatus === 'cancelled') {
      // When cancelling, refund credits for all confirmed participants
      await refundAllParticipants(workoutId, workout.title);
    }

    // Update workout status
    await db
      .update(workouts)
      .set({
        status: newStatus,
        updatedAt: new Date(),
      })
      .where(eq(workouts.id, workoutId));

    return {
      success: true,
      message: `Workout status updated to ${newStatus}`,
      newStatus,
    };
  } catch (error) {
    console.error('Error transitioning workout status:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Transitions a participant to a new status with credit handling
 */
export async function transitionParticipantStatus(
  workoutId: string,
  participantId: string,
  newStatus: ParticipantStatus,
  trainerId: string
): Promise<ParticipantStatusTransitionResult> {
  try {
    // Get participant with current status and workout info
    const [participant] = await db
      .select({
        id: workoutParticipants.id,
        customerId: workoutParticipants.customerId,
        status: workoutParticipants.status,
        creditDeducted: workoutParticipants.creditDeducted,
        workoutTitle: workouts.title,
        workoutStatus: workouts.status,
        trainerId: workouts.trainerId,
      })
      .from(workoutParticipants)
      .innerJoin(workouts, eq(workoutParticipants.workoutId, workouts.id))
      .where(
        and(
          eq(workoutParticipants.id, participantId),
          eq(workoutParticipants.workoutId, workoutId),
          eq(workouts.trainerId, trainerId)
        )
      );

    if (!participant) {
      return { success: false, message: 'Participant not found' };
    }

    // Validate status transition
    const transitionValidation = validateParticipantStatusTransition(
      participant.status as ParticipantStatus,
      newStatus
    );

    if (!transitionValidation.valid) {
      return { success: false, message: transitionValidation.message };
    }

    let creditDeducted = participant.creditDeducted;

    // Handle credit deduction/refund based on status change
    if (newStatus === 'confirmed' && !participant.creditDeducted) {
      // Deduct credit when confirming participant
      await deductCreditForSession(
        participant.customerId,
        workoutId,
        participantId,
        participant.workoutTitle
      );
      creditDeducted = true;
    } else if (newStatus === 'cancelled' && participant.creditDeducted) {
      // Refund credit when cancelling confirmed participant
      await refundCreditForSession(
        participant.customerId,
        workoutId,
        participantId,
        participant.workoutTitle
      );
      creditDeducted = false;
    }

    // Prepare update data
    const updateValues: any = {
      status: newStatus,
      creditDeducted,
    };

    // Set confirmed timestamp if status is confirmed
    if (newStatus === 'confirmed') {
      updateValues.confirmedAt = new Date();
    } else if (newStatus === 'enrolled') {
      updateValues.confirmedAt = null;
    }

    // Update participant
    await db
      .update(workoutParticipants)
      .set(updateValues)
      .where(eq(workoutParticipants.id, participantId));

    return {
      success: true,
      message: `Participant status updated to ${newStatus}`,
      newStatus,
      creditDeducted,
    };
  } catch (error) {
    console.error('Error transitioning participant status:', error);
    return {
      success: false,
      message: error instanceof Error ? error.message : 'Unknown error occurred',
    };
  }
}

/**
 * Confirms all enrolled participants and deducts credits
 */
async function confirmAllParticipants(workoutId: string, workoutTitle: string) {
  const enrolledParticipants = await db
    .select({
      id: workoutParticipants.id,
      customerId: workoutParticipants.customerId,
      creditDeducted: workoutParticipants.creditDeducted,
    })
    .from(workoutParticipants)
    .where(
      and(
        eq(workoutParticipants.workoutId, workoutId),
        eq(workoutParticipants.status, 'enrolled')
      )
    );

  for (const participant of enrolledParticipants) {
    // Deduct credit if not already deducted
    if (!participant.creditDeducted) {
      await deductCreditForSession(
        participant.customerId,
        workoutId,
        participant.id,
        workoutTitle
      );
    }

    // Update participant status
    await db
      .update(workoutParticipants)
      .set({
        status: 'confirmed',
        confirmedAt: new Date(),
        creditDeducted: true,
      })
      .where(eq(workoutParticipants.id, participant.id));
  }
}

/**
 * Refunds credits for all confirmed participants
 */
async function refundAllParticipants(workoutId: string, workoutTitle: string) {
  const confirmedParticipants = await db
    .select({
      id: workoutParticipants.id,
      customerId: workoutParticipants.customerId,
      creditDeducted: workoutParticipants.creditDeducted,
    })
    .from(workoutParticipants)
    .where(
      and(
        eq(workoutParticipants.workoutId, workoutId),
        eq(workoutParticipants.status, 'confirmed')
      )
    );

  for (const participant of confirmedParticipants) {
    // Refund credit if it was deducted
    if (participant.creditDeducted) {
      await refundCreditForSession(
        participant.customerId,
        workoutId,
        participant.id,
        workoutTitle
      );
    }

    // Update participant status to cancelled
    await db
      .update(workoutParticipants)
      .set({
        status: 'cancelled',
        creditDeducted: false,
      })
      .where(eq(workoutParticipants.id, participant.id));
  }
}
