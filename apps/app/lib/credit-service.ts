import { db, creditTransactions, customers } from '@workspace/auth/server';
import { eq } from 'drizzle-orm';
import type { NewCreditTransaction } from '@workspace/auth/server';

export interface CreateCreditTransactionParams {
  customerId: string;
  type: 'purchase' | 'deduction' | 'refund' | 'adjustment';
  amount: number; // positive for credits added, negative for credits deducted
  description: string;
  relatedPurchaseId?: string;
  relatedWorkoutId?: string;
  relatedParticipantId?: string;
}

/**
 * Creates a credit transaction and updates customer balance atomically
 */
export async function createCreditTransaction(params: CreateCreditTransactionParams) {
  const {
    customerId,
    type,
    amount,
    description,
    relatedPurchaseId,
    relatedWorkoutId,
    relatedParticipantId,
  } = params;

  return await db.transaction(async (tx) => {
    // Get current customer balance
    const [customer] = await tx
      .select({ sessionCredits: customers.sessionCredits })
      .from(customers)
      .where(eq(customers.id, customerId));

    if (!customer) {
      throw new Error('Customer not found');
    }

    const balanceBefore = customer.sessionCredits;
    const balanceAfter = balanceBefore + amount;

    // Validate that balance won't go negative for deductions
    if (type === 'deduction' && balanceAfter < 0) {
      throw new Error('Insufficient credits for deduction');
    }

    // Create credit transaction record
    const [transaction] = await tx
      .insert(creditTransactions)
      .values({
        customerId,
        type,
        amount,
        balanceBefore,
        balanceAfter,
        description,
        relatedPurchaseId,
        relatedWorkoutId,
        relatedParticipantId,
      })
      .returning();

    // Update customer balance
    await tx
      .update(customers)
      .set({
        sessionCredits: balanceAfter,
        updatedAt: new Date(),
      })
      .where(eq(customers.id, customerId));

    return transaction;
  });
}

/**
 * Deducts a credit for a workout session
 */
export async function deductCreditForSession(
  customerId: string,
  workoutId: string,
  participantId: string,
  workoutTitle: string
) {
  return await createCreditTransaction({
    customerId,
    type: 'deduction',
    amount: -1, // Deduct 1 credit
    description: `Credit deducted for workout session: ${workoutTitle}`,
    relatedWorkoutId: workoutId,
    relatedParticipantId: participantId,
  });
}

/**
 * Refunds a credit for a cancelled session
 */
export async function refundCreditForSession(
  customerId: string,
  workoutId: string,
  participantId: string,
  workoutTitle: string
) {
  return await createCreditTransaction({
    customerId,
    type: 'refund',
    amount: 1, // Add 1 credit back
    description: `Credit refunded for cancelled workout session: ${workoutTitle}`,
    relatedWorkoutId: workoutId,
    relatedParticipantId: participantId,
  });
}

/**
 * Records credit addition from purchase
 */
export async function addCreditsFromPurchase(
  customerId: string,
  purchaseId: string,
  creditsAdded: number
) {
  return await createCreditTransaction({
    customerId,
    type: 'purchase',
    amount: creditsAdded,
    description: `Credits added from purchase: ${creditsAdded} session${creditsAdded > 1 ? 's' : ''}`,
    relatedPurchaseId: purchaseId,
  });
}

/**
 * Manual credit adjustment (admin function)
 */
export async function adjustCredits(
  customerId: string,
  amount: number,
  reason: string
) {
  return await createCreditTransaction({
    customerId,
    type: 'adjustment',
    amount,
    description: `Manual adjustment: ${reason}`,
  });
}
