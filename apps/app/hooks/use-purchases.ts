'use client';

import useSWR, { mutate as globalMutate } from 'swr';
import { toast } from 'sonner';
import type {
  PurchaseResponse,
  CreatePurchaseInput,
  PurchaseQueryInput,
  PaginatedResponse,
} from '@/lib/validations';

export const fetcher = (url: string) => fetch(url).then(res => {
  if (!res.ok) throw new Error('Failed to fetch');
  return res.json();
});

export function getPurchasesKey(customerId: string, params?: Partial<PurchaseQueryInput>) {
  const searchParams = new URLSearchParams();
  if (params?.limit) searchParams.set('limit', params.limit.toString());
  if (params?.offset) searchParams.set('offset', params.offset.toString());
  if (params?.sortBy) searchParams.set('sortBy', params.sortBy);
  if (params?.sortOrder) searchParams.set('sortOrder', params.sortOrder);
  return `/api/customers/${customerId}/purchases?${searchParams.toString()}`;
}

export function usePurchasesList(customerId: string, params?: Partial<PurchaseQueryInput>) {
  const key = customerId ? getPurchasesKey(customerId, params) : null;
  const { data, error, isLoading, mutate } = useSWR<PaginatedResponse<PurchaseResponse>>(key, fetcher);
  return {
    purchases: data?.data || [],
    loading: isLoading,
    error: error ? (error instanceof Error ? error.message : 'An error occurred') : null,
    pagination: data?.pagination || { total: 0, limit: 20, offset: 0, hasMore: false },
    mutate,
  };
}

export async function createPurchase(customerId: string, purchaseData: CreatePurchaseInput, params?: Partial<PurchaseQueryInput>) {
  try {
    const response = await fetch(`/api/customers/${customerId}/purchases`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(purchaseData),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to create purchase');
    }
    const newPurchase: PurchaseResponse = await response.json();
    toast.success('Purchase created successfully');
    await globalMutate(getPurchasesKey(customerId, params));
    return newPurchase;
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : 'An error occurred';
    toast.error(errorMessage);
    throw error;
  }
}
