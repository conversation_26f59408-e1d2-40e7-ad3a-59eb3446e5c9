import { NextRequest } from 'next/server';
import { db, workouts, workoutParticipants, customers } from '@workspace/auth/server';
import { eq, and, count } from 'drizzle-orm';
import { addParticipantSchema } from '@/lib/validations';
import {
  withAuth,
  validateRequestBody,
  createSuccessResponse,
  createErrorResponse,
  handleApiError,
  getTrainerIdFromUser,
} from '@/lib/api-utils';

// GET /api/workouts/[id]/participants - Get workout participants
export const GET = withAuth(async (_request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    // Verify workout belongs to trainer
    const [workout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!workout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Get participants
    const participants = await db
      .select({
        id: workoutParticipants.id,
        workoutId: workoutParticipants.workoutId,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.workoutId, id));

    return createSuccessResponse(participants);
  } catch (error) {
    return handleApiError(error);
  }
});

// POST /api/workouts/[id]/participants - Add participant to workout
export const POST = withAuth(async (request: NextRequest, user, { params }: { params: Promise<{ id: string }> }) => {
  try {
    const { id } = await params;
    const trainerId = await getTrainerIdFromUser(user);

    if (!trainerId) {
      return createErrorResponse('Forbidden', 'Invalid trainer access', 403);
    }

    const bodyValidation = await validateRequestBody(request, addParticipantSchema);
    if (!bodyValidation.success) {
      return createErrorResponse(
        bodyValidation.error.error,
        bodyValidation.error.message,
        400,
        bodyValidation.error.details
      );
    }

    const { customerId } = bodyValidation.data;

    // Verify workout belongs to trainer
    const [workout] = await db
      .select()
      .from(workouts)
      .where(and(eq(workouts.id, id), eq(workouts.trainerId, trainerId)));

    if (!workout) {
      return createErrorResponse('Not Found', 'Workout not found', 404);
    }

    // Verify customer belongs to trainer and has credits
    const [customer] = await db
      .select()
      .from(customers)
      .where(and(eq(customers.id, customerId), eq(customers.trainerId, trainerId)));

    if (!customer) {
      return createErrorResponse('Not Found', 'Customer not found', 404);
    }

    if (customer.sessionCredits <= 0) {
      return createErrorResponse('Bad Request', 'Customer has no session credits available', 400);
    }

    // Check if customer is already enrolled
    const [existingParticipant] = await db
      .select()
      .from(workoutParticipants)
      .where(and(eq(workoutParticipants.workoutId, id), eq(workoutParticipants.customerId, customerId)));

    if (existingParticipant) {
      return createErrorResponse('Conflict', 'Customer is already enrolled in this workout', 409);
    }

    // Check workout capacity
    const [participantCountResult] = await db
      .select({ participantCount: count() })
      .from(workoutParticipants)
      .where(eq(workoutParticipants.workoutId, id));

    if (!participantCountResult) {
      throw new Error('Failed to fetch participant count');
    }

    const { participantCount } = participantCountResult;

    if (participantCount >= workout.maxParticipants) {
      return createErrorResponse('Bad Request', 'Workout is at maximum capacity', 400);
    }

    // Add participant
    const [newParticipant] = await db
      .insert(workoutParticipants)
      .values({
        workoutId: id,
        customerId,
        status: 'enrolled',
      })
      .returning();

    if (!newParticipant) {
      throw new Error('Failed to add participant');
    }

    // Get participant with customer name for response
    const [participantWithCustomer] = await db
      .select({
        id: workoutParticipants.id,
        workoutId: workoutParticipants.workoutId,
        customerId: workoutParticipants.customerId,
        customerName: customers.name,
        status: workoutParticipants.status,
        enrolledAt: workoutParticipants.enrolledAt,
        confirmedAt: workoutParticipants.confirmedAt,
        creditDeducted: workoutParticipants.creditDeducted,
      })
      .from(workoutParticipants)
      .innerJoin(customers, eq(workoutParticipants.customerId, customers.id))
      .where(eq(workoutParticipants.id, newParticipant.id));

    return createSuccessResponse(participantWithCustomer, 201);
  } catch (error) {
    return handleApiError(error);
  }
});
