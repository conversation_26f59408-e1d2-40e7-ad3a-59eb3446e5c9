'use client';

import { useDraggable } from '@dnd-kit/core';
import { CSS } from '@dnd-kit/utilities';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { RiUserLine, RiCoinLine } from '@remixicon/react';
import type { CustomerResponse } from '@/lib/validations';

interface DraggableCustomerItemProps {
  customer: CustomerResponse;
  isDragging?: boolean;
}

export function DraggableCustomerItem({ customer, isDragging = false }: DraggableCustomerItemProps) {
  const { attributes, listeners, setNodeRef, transform } = useDraggable({
    id: `customer-${customer.id}`,
    data: {
      type: 'customer',
      customer,
    },
  });

  const style = {
    transform: CSS.Translate.toString(transform),
  };

  const hasCredits = customer.sessionCredits > 0;

  return (
    <Card
      ref={setNodeRef}
      style={style}
      className={`
        cursor-grab active:cursor-grabbing transition-all duration-200
        ${isDragging ? 'opacity-50 scale-95' : 'hover:shadow-md'}
        ${!hasCredits ? 'opacity-60 border-muted' : 'border-border'}
      `}
      {...listeners}
      {...attributes}
    >
      <CardContent className="p-3">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2 min-w-0 flex-1">
            <div className="flex-shrink-0">
              <RiUserLine className="h-4 w-4 text-muted-foreground" />
            </div>
            <div className="min-w-0 flex-1">
              <p className="font-medium text-sm truncate">{customer.name}</p>
              {customer.email && <p className="text-xs text-muted-foreground truncate">{customer.email}</p>}
            </div>
          </div>

          <div className="flex items-center gap-1 flex-shrink-0">
            <RiCoinLine className="h-3 w-3 text-muted-foreground" />
            <Badge variant={hasCredits ? 'default' : 'secondary'} className="text-xs">
              {customer.sessionCredits}
            </Badge>
          </div>
        </div>

        {!hasCredits && <div className="mt-2 text-xs text-muted-foreground">No credits available</div>}
      </CardContent>
    </Card>
  );
}
